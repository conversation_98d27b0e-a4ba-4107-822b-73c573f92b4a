import axios, { AxiosInstance } from 'axios';

// 响应接口定义
export interface PlaygroundActiveResponse {
    code: string;
    requestId: string;
    result: boolean; // 是否激活
}

// 创建axios实例，baseURL为https://console.bce.baidu.com，请求头中加入x-region: bd
const dataBuilderApi: AxiosInstance = axios.create({
    baseURL: 'https://console.bce.baidu.com',
    headers: {
        'x-region': 'bd',
        'Content-Type': 'application/json'
    }
});

// 获取playground激活状态
export const getPlaygroundActive = async (): Promise<PlaygroundActiveResponse> => {
    try {
        const response = await dataBuilderApi.get<PlaygroundActiveResponse>('/api/databuilder/v1/playground/active');
        return response.data;
    } catch (error) {
        console.error('获取playground激活状态失败:', error);
        throw error;
    }
};

// 导出axios实例，供其他API使用
export default dataBuilderApi;